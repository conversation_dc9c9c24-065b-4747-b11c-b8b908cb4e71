package id.co.kiwoom.kwis.config.menu;

import id.co.kiwoom.kwis.common.exception.BusinessException;
import id.co.kiwoom.kwis.common.util.RequestUtil;
import id.co.kiwoom.kwis.common.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * URI �젙蹂대�� 硫붾え由ъ뿉 �긽二쇱떆�궎怨� URL�뿉 ����븳 �젒洹쇨텒�븳�쓣 �솗�씤�븯嫄곕굹  �솕硫댁뿉�꽌 �궗�슜�븷 硫붾돱紐⑸줉�쓣 �깮�꽦�븳�떎.
 *
 * <AUTHOR> �옣�젙�썕
 * */
public class UriManager {

    private static Logger logger = LoggerFactory.getLogger(UriManager.class);
	private static final String ln	= "\n";

    private static UriManager instance;
    private JXParser uriXml;

    private Map<String,String> menus = new HashMap<>();
    private Map<String,String> pcMenuHtml = new HashMap<>();
    private Map<String,String> mobMenuHtml = new HashMap<>();

    private UriManager(){}
	private static Properties prop;

	/**
	 * XML �뙆�씪�쓣 �씠�슜�븯�뿬 UriManager 珥덇린�솕
	 *
	 * @return UriManager
	 * @throws Exception
	 * */
    public static UriManager initInstance() throws Exception{
    	synchronized(UriManager.class){
    		if(instance==null){
    			instance = new UriManager();
    			instance.init();
    			instance.makeMenus();
    			instance.makePcMenuHtml();
    			instance.makeMobMenuHtml();
    		}
    		return instance;
    	}
    }

	/**
	 * UriManager getInstance
	 *
	 * @return UriManager
	 * @throws Exception
	 * */
    public static UriManager getInstance() throws Exception{
    	synchronized(UriManager.class){
    		if(instance==null)
    			throw new Exception("UriManager not initialized.");
    		return instance;
    	}
    }

	/**
	 * UriManager Initialize
	 *
	 * @throws Exception
	 * */
	private void init() throws Exception{
		InputStream is = null;
		try {
			prop = new Properties();
			is = Thread.currentThread().getContextClassLoader().getResourceAsStream("application.properties");
			prop.load(is);

			String path = prop.getProperty("menu.file.base");
			path = path.replaceAll("\\.", "");
			String fileName = prop.getProperty("menu.file.name");
			fileName = fileName.replaceAll("/", "");

			uriXml	= new JXParser(new File(path, fileName));
		} catch (FileNotFoundException e) {
			logger.error("application.properties load failed !! " + e);
		} catch (IOException e) {
			logger.error("application.properties load failed !! " + e);
		} finally {
			if(is != null) is.close();
		}
	}

	/**
	 * Create a Javascript Menu String
	 *
	 * @throws Exception
	 * */
	private void makeMenus() throws Exception{

		StringBuilder sb	= new StringBuilder();
		sb.append("var $menusObject = [").append(ln);

		Element[] childs	= uriXml.getElements(uriXml.getRootElement(), "info[@exps='Y']");
		for(int i=0;i<childs.length;i++){
			makeMenus(childs[i], sb);
			sb.append(i==childs.length-1?"":",").append(ln);
		}
		sb.append(ln).append("];");

		menus.put(String.valueOf(0), sb.toString());
	}

	/**
	 * Create a Javascript Menu String
	 *
	 * @throws Exception
	 * */
	private void makeMenus(Element info, StringBuilder sb) throws Exception{
		String indent	= StringUtil.append("\t", Integer.parseInt(uriXml.getAttribute(info,"lvl")));
		sb.append(indent).append("{").append(ln)
			.append(indent).append("\"lvl\":").append("\"").append(uriXml.getAttribute(info,"lvl")).append("\"").append(ln)
			.append(indent).append(",").append("\"ucd\":").append("\"").append(uriXml.getAttribute(info,"ucd")).append("\"").append(ln)
			.append(indent).append(",").append("\"url\":").append("\"").append(uriXml.getValue(uriXml.getElement(info,"url"))).append("\"").append(ln)
			.append(indent).append(",").append("\"name\":").append("\"").append(uriXml.getValue(uriXml.getElement(info,"name"))).append("\"").append(ln)
			.append(indent).append(",").append("\"multi\":").append("\"").append(uriXml.getValue(uriXml.getElement(info,"multi"))).append("\"").append(ln)
			;

		Element[] childs	= uriXml.getElements(info, "info[@exps='Y']");
		if(childs.length>0)
			sb.append(indent).append(",").append("\"childs\":[").append(ln);
		for(int i=0;i<childs.length;i++){
			makeMenus(childs[i], sb);
			sb.append(i==childs.length-1?"":",").append(ln);
		}
		if(childs.length>0)
			sb.append(indent).append("]").append(ln);
		sb.append(indent).append("}");
	}

	/**
	 * Create PC MENU HTML
	 *
	 * @throws Exception
	 * */
	private void makePcMenuHtml() throws Exception{
		StringBuilder sb = new StringBuilder();
		sb.append("<ol class=\"global-menu-pc\">").append(ln);

		Element nameKey1 = null;
		Element nameKey2 = null;
		Element urlKey1 = null;
		Element urlKey2 = null;

		String nameValue1 = null;
		String name2Value2 = null;
		String urlValue1 = null;
		String urlValue2 = null;

		Element[] c$1 = uriXml.getElements(uriXml.getRootElement(), "info[@exps='Y']");

		for(int i=0;i<c$1.length;i++){
			nameKey1 = uriXml.getElement(c$1[i],"name");
			nameValue1 = nameKey1==null?"":uriXml.getValue(nameKey1);

			urlKey1 = uriXml.getElement(c$1[i],"url");
			urlValue1 = urlKey1==null?"":uriXml.getValue(urlKey1);

			sb.append("<li class=\"menu-item\">").append(ln);
			sb.append("<a href=\"javascript:void(0);\" class=\"menu-name\"");

			if(StringUtils.isNotEmpty(urlValue1)) {
				sb.append(" onclick=\"")
				.append("javascript:kidCommon.goMenu('")
				.append(urlValue1)
				.append("');")
				.append("\"");
			}

			sb.append(" data-ucd=\"")
			.append(uriXml.getAttribute(c$1[i],"ucd"))
			.append("\">")
			.append(nameValue1)
			.append("</a>").append(ln);

			Element[] c$2 = uriXml.getElements(c$1[i], "info[@exps='Y']");
			if(c$2.length > 0) {
				sb.append("<ul class='sub-ul-menu'>").append(ln);
				for(int j=0;j<c$2.length;j++){
					nameKey2 = uriXml.getElement(c$2[j],"name");
					name2Value2 = nameKey2==null?"":uriXml.getValue(nameKey2);

					urlKey2 = uriXml.getElement(c$2[j],"url");
					urlValue2 = urlKey2==null?"":uriXml.getValue(urlKey2);

					sb.append("<li>")
					.append("<a href=\"javascript:void(0);\"");

					if(StringUtils.isNotEmpty(urlValue2)) {
						sb.append(" onclick=\"")
						.append("javascript:kidCommon.goMenu('")
						.append(urlValue2)
						.append("');")
						.append("\"");
					}

					sb.append(" data-ucd=\"")
					.append(uriXml.getAttribute(c$2[j],"ucd"))
					.append("\">")
					.append(name2Value2)
					.append("</a>");
					sb.append("</li>").append(ln);
				}
				sb.append("</ul>").append(ln);
			}
			sb.append("</li>").append(ln);
		}
		sb.append("</ol>").append(ln);

		pcMenuHtml.put(String.valueOf(0), sb.toString());
	}

	/**
	 * Create Mobile MENU HTML
	 *
	 * @throws Exception
	 * */
	private void makeMobMenuHtml() throws Exception{
		StringBuilder sb = new StringBuilder();
		sb.append("<ol>").append(ln);

		Element nameKey1 = null;
		Element nameKey2 = null;
		Element urlKey1 = null;
		Element urlKey2 = null;

		String nameValue1 = null;
		String name2Value2 = null;
		String urlValue1 = null;
		String urlValue2 = null;

		Element[] c$1 = uriXml.getElements(uriXml.getRootElement(), "info[@exps='Y']");

		for(int i=0;i<c$1.length;i++){
			nameKey1 = uriXml.getElement(c$1[i],"name");
			nameValue1 = nameKey1==null?"":uriXml.getValue(nameKey1);

			urlKey1 = uriXml.getElement(c$1[i],"url");
			urlValue1 = urlKey1==null?"":uriXml.getValue(urlKey1);

			sb.append("<li class=\"menu-item\">").append(ln);
			sb.append("<a href=\"javascript:void(0);\" class=\"menu-name\"");

			/*
			if(StringUtils.isNotEmpty(urlValue1)) {
				sb.append(" onclick=\"")
				.append("javascript:kidCommon.goMenu('")
				.append(urlValue1)
				.append("');")
				.append("\"");
			}
			*/

			sb.append(" data-ucd=\"")
			.append(uriXml.getAttribute(c$1[i],"ucd"))
			.append("\">")
			.append(nameValue1)
			.append("</a>").append(ln);

			Element[] c$2 = uriXml.getElements(c$1[i], "info[@exps='Y']");
			if(c$2.length > 0) {
				sb.append("<ul>").append(ln);
				for(int j=0;j<c$2.length;j++){
					nameKey2 = uriXml.getElement(c$2[j],"name");
					name2Value2 = nameKey2==null?"":uriXml.getValue(nameKey2);

					urlKey2 = uriXml.getElement(c$2[j],"url");
					urlValue2 = urlKey2==null?"":uriXml.getValue(urlKey2);

					sb.append("<li>")
					.append("<a href=\"javascript:void(0);\"");

					if(StringUtils.isNotEmpty(urlValue2)) {
						sb.append(" onclick=\"")
						.append("javascript:kidCommon.goMenu('")
						.append(urlValue2)
						.append("');")
						.append("\"");
					}

					sb.append(" data-ucd=\"")
					.append(uriXml.getAttribute(c$2[j],"ucd"))
					.append("\">")
					.append(name2Value2)
					.append("</a>");
					sb.append("</li>").append(ln);
				}
				sb.append("</ul>").append(ln);
			}
			sb.append("</li>").append(ln);
		}
		sb.append("</ol>").append(ln);

		mobMenuHtml.put(String.valueOf(0), sb.toString());
	}

	/**
	 * �솕硫댁뿉�꽌 �궗�슜�븷 硫붾돱 紐⑸줉�쓣 �뒪�겕由쏀듃 臾몄옄�뿴濡� 諛섑솚 (Returns the menu list to be used on the screen as a script string)
	 *
	 * @return String
	 * */
	public String getMenuScript(){
		return menus.get(String.valueOf(0));
	}

	/**
	 * �솕硫댁뿉�꽌 �궗�슜�븷 PC 硫붾돱 HTML 諛섑솚 (Return PC Menu HTML to use on the screen)
	 *
	 * @return String
	 * */
	public String getPcMenuHtml(){
		return pcMenuHtml.get(String.valueOf(0));
	}

	/**
	 * �솕硫댁뿉�꽌 �궗�슜�븷 MOBILE 硫붾돱 HTML 諛섑솚 (Return MOBILE Menu HTML to use on the screen)
	 *
	 * @return String
	 * */
	public String getMobMenuHtml(){
		return mobMenuHtml.get(String.valueOf(0));
	}

	/**
	 * javascript 硫붾돱 臾몄옄�뿴�쓣 response outstream�쑝濡� �쟾�넚
	 *
	 * @throws Exception
	 * */
	public void writeMenuScript(HttpServletRequest request, HttpServletResponse response) throws Exception{
    	response.setStatus(HttpServletResponse.SC_OK);
    	response.setHeader("Content-Type", "application/javascript; charset=UTF-8");
    	response.getWriter().append( ln+getMenuScript() );

		//�쁽�옱�솕硫댁쓽 uri濡� 硫붾돱 navi 異붿텧(/script-menu include �떆�젏)
		String refererMenuScript = UriManager.getInstance().getRefererUriTree(request);
		//�쁽�옱 uri濡� 硫붾돱 navi 異붿텧(�솕硫� forwarding �떆�젏)
		//String currentMenuScript	= UriManager.getInstance().getCurrentUriTree(request);
    	response.getWriter().append(ln+"var $currentMenus=").append( refererMenuScript ).append(";");
		response.getWriter().close();
	}

	private Element getUrlElement(String url) throws Exception{
		String matchUrl	= url;

		if("".equals(matchUrl))
			matchUrl	= "/";
		Element[] infoE	= uriXml.getElements("//info[url='"+matchUrl+"']");

		return (infoE==null||infoE.length==0)?null:infoE[infoE.length-1];
	}

	public String getCurrentUriTree(HttpServletRequest request){
		String uri	= RequestUtil.filterUri(request.getRequestURI());
		return getCurrentUriTree(uri);
	}

	public String getRefererUriTree(HttpServletRequest request){
		String uri	= RequestUtil.getReferUri(request);
		return getCurrentUriTree("/"+uri);
	}

	/**
	 * �쁽�옱 request�뿉 �빐�떦�븯�뒗 硫붾돱 �젙蹂대�� �떞怨좎엳�뒗 script臾몄옄�뿴�쓣 諛섑솚�븳�떎. (Returns a script string containing menu information corresponding to the current request)
	 *
	 * @param String request uri
	 * @return String Menu Script
	 * */
	public String getCurrentUriTree(String uri){
		StringBuilder sb	= new StringBuilder();
		try{
			Element infoE	= getUrlElement(uri);

			/*
			if(infoE==null)
				throw new Exception("Undefined URL ["+uri+"].");
			*/

			if(infoE==null)
				throw new BusinessException("Undefined URL ["+uri+"].");

			logger.debug("URL matched ["+uri+"] - [�젅踰� "+uriXml.getAttribute(infoE,"lvl")+"] - [肄붾뱶 "+uriXml.getAttribute(infoE,"ucd")+"]");

			List<Element> trees	= new ArrayList<>();
			trees.add(infoE);

			Element parent	= infoE;
			while(parent.getParent()!=uriXml.getRootElement()){
				parent	= parent.getParent();
				if(parent==null || parent==uriXml.getRootElement())
					break;
				trees.add(parent);
			}

			sb.append("[");
			for(int i=trees.size()-1;i>=0;i--){
				sb.append(i==trees.size()-1?"":",").append("{")
				.append("\"lvl\":").append("\"").append(uriXml.getAttribute(trees.get(i),"lvl")).append("\"")
				.append(",").append("\"ucd\":").append("\"").append(uriXml.getAttribute(trees.get(i),"ucd")).append("\"")
				.append(",").append("\"url\":").append("\"").append(uriXml.getValue(uriXml.getElement(trees.get(i),"url"))).append("\"")
				.append(",").append("\"name\":").append("\"").append(uriXml.getValue(uriXml.getElement(trees.get(i),"name"))).append("\"")
				.append(",").append("\"exps\":").append("\"").append(uriXml.getAttribute(trees.get(i),"exps")).append("\"")
				.append(",").append("\"multi\":").append("\"").append(uriXml.getAttribute(trees.get(i),"multi")).append("\"")
				.append("}");
			}
			sb.append("]");
		}catch(BusinessException e) {
			logger.debug(e.getMessage());
			sb.setLength(0);
			sb.append("[]");
		}catch(NullPointerException e) {
			logger.error(e.getMessage());
		}catch(Exception e){
			if(logger.isWarnEnabled())
				logger.warn(e.getMessage());
			sb.setLength(0);
			sb.append("[]");
		}
		return sb.toString();
	}

	public UriVo getUrlInfo(HttpServletRequest request) throws Exception{
		String uri	= RequestUtil.filterUri(RequestUtil.getRequestURI(request));
		return getUrlInfo(uri);
	}

	public UriVo getUrlInfo(String uri) throws Exception{
		Element infoE	= getUrlElement(uri);
		if(infoE==null)
			return null;

		UriVo info	= new UriVo();
		info.setLvl(uriXml.getAttribute(infoE,"lvl"));
		info.setUcd(uriXml.getAttribute(infoE,"ucd"));
		info.setUrl(uri);
		info.setName(uriXml.getValue(uriXml.getElement(infoE,"name")));
		info.setUucd(uriXml.getAttribute(infoE,"uucd"));
		info.setExps(uriXml.getAttribute(infoE,"exps"));
		info.setMulti(uriXml.getAttribute(infoE,"multi"));

		return info;
	}

	public String getCurrentUri(HttpServletRequest request){
		String uri	= RequestUtil.filterUri(request.getRequestURI());
		return getCurrentUri(uri);
	}

	/**
	 * �쁽�옱 request�뿉 �빐�떦�븯�뒗 硫붾돱 �젙蹂대�� �떞怨좎엳�뒗 script臾몄옄�뿴�쓣 諛섑솚�븳�떎. (Returns a script string containing menu information corresponding to the current request)
	 *
	 * @param String request uri
	 * @return String Menu Script
	 * */
	public String getCurrentUri(String uri){
		StringBuilder sb	= new StringBuilder();
		try{
			UriVo uriVo = getUrlInfo(uri);
			if(uriVo == null) {
				sb.append("[]");
				return sb.toString();
			}

			sb.append("[");
				sb.append("{")
				.append("\"lvl\":").append("\"").append(uriVo.getLvl()).append("\"")
				.append(",").append("\"ucd\":").append("\"").append(uriVo.getUcd()).append("\"")
				.append(",").append("\"url\":").append("\"").append(uriVo.getUrl()).append("\"")
				.append(",").append("\"name\":").append("\"").append(uriVo.getName()).append("\"")
				.append(",").append("\"exps\":").append("\"").append(uriVo.getExps()).append("\"")
				.append(",").append("\"multi\":").append("\"").append(uriVo.getMulti()).append("\"")
				.append("}");
			sb.append("]");

		}catch(NullPointerException e) {
			logger.error(e.getMessage());
		}catch(Exception e){
			if(logger.isWarnEnabled())
				logger.warn(e.getMessage());
			sb.setLength(0);
			sb.append("[]");
		}
		return sb.toString();
	}

	/*
	public UriVo getUrlInfoFromUcd(String ucd) throws Exception{
		Element infoE	= uriXml.getElement("//info[@ucd='"+ucd+"']");
		if(infoE==null)
			return null;

		UriVo info	= new UriVo();
		info.setLvl(uriXml.getAttribute(infoE,"lvl"));
		info.setUcd(uriXml.getAttribute(infoE,"ucd"));
		info.setUrl(uriXml.getValue(uriXml.getElement(infoE,"url")));
		info.setUucd(uriXml.getAttribute(infoE,"uucd"));
		info.setExps(uriXml.getAttribute(infoE,"exps"));

		return info;
	}
	*/

	/**
	 * 二쇱뼱吏� ucd(�솕硫댁퐫�뱶)�쓽 url�쓣 諛섑솚�븳�떎.
	 * �쁽�옱 uri�쓽 �룄硫붿씤肄붾뱶��� 二쇱뼱吏� ucd�쓽 �룄硫붿씤 肄붾뱶媛� �떎瑜대떎硫� full url濡� 諛섑솚�븳�떎
	 *
	 * @param String ucd �솕硫퀹D
	 * @return String url
	 * @throws Exception
	 * @throws Exception
	 * */
	/*
	public String getUriFromUcd(HttpServletRequest request, String ucd) throws Exception{
		if(!ucd.matches("^[a-zA-Z0-9\\$]{4,50}$"))
			throw new RuntimeException(String.format("�삱諛붾Ⅴ吏� �븡��� redirect ����엯 [%s] �엯�땲�떎.",ucd));

		String rUrl		= uriXml.getValue("//info[@ucd='"+ucd+"']/url");

		//�쁽�옱�쓽 domain�쓣 �쑀吏��븯硫댁꽌 redirect�븷 寃쎌슦 2021.06.28 �옣�젙�썕
		String internalHost	= request.getRequestURL().toString();
		int hostIdx	= internalHost.indexOf("/",8);
		if(hostIdx>0)
			internalHost	= internalHost.substring(0,hostIdx);
		if(!internalHost.endsWith("/") && !rUrl.startsWith("/"))
			rUrl	= "/"+rUrl;

		return internalHost+rUrl;
	}
	*/
}
