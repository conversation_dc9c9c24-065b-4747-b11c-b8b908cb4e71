#JMX
spring.jmx.enabled=true
###################################################################################################

# JNDI
jdbc.jndi=false
jdbc.jndi.name=kwibst1

# JDBC - Local Development Database Configuration
jdbc.driver-class-name=net.sf.log4jdbc.DriverSpy
jdbc.url=********************************************
jdbc.username=kiwoom
jdbc.password=your_local_password
jdbc.initial-size=3
jdbc.max-total=50
jdbc.max-idle=50
jdbc.max-wait-millis=10000
jdbc.validation-query=SELECT 1 FROM DUAL

###################################################################################################

# FILE UPLOAD - Local paths
file.upload.dir=/tmp/datafiles/upload/

# BANNER JSON PATH
file.upload.banner-json-save-dir=${file.upload.dir}banner/json/

# HOMEPAGE TOP BANNER JSON FILENAME
home.top.banner.json.filename=main_banner01.json

# HTS FRONTPAGE TOP BANNER JSON FILENAME
hts.top.banner.json.filename=frontpage_banner01.json

# HTS FRONTPAGE TOP BOTTOM JSON FILENAME
hts.bottom.banner.json.filename=frontpage_banner02.json

# POPUP JSON PATH
file.upload.popup-json-save-dir=${file.upload.dir}popup/json/

# HOMEPAGE POPUP JSON FILENAME
homepage.popup.json.filename=homepage_popup_info.json

# HTS POPUP JSON FILENAME
hts.popup.json.filename=hts_popup_info.json

# HTS POPUP PAGE URL
hts.popup.page.url=/hts/popup/getPopupMain

###################################################################################################

# MENU - Local development path
menu.file.base=/Users/<USER>/Desktop/DAOU/Side Project/indo-web/indo-web-dev/src/main/resources/
menu.file.name=menu.xml

###################################################################################################

# Secret-key
auth.secret-key=gweBvuXHfhpiwXmnt3eXpzCxEmBr5eFr

# AUTH
auth.login.success-url=/
auth.login.entry-url=/auth/loginView 

# Prometheus Exporter
prometheus.server.instance01.port=9094
prometheus.server.instance02.port=9094

# Email Send API URL - Local development
email.send.server.url=http://localhost:8092/api/sendemail
