tcpc.startup = true
tcpc.debug = true
tcpc.count = 3
#tcpc.htstype = H4

tcpc.sndEnc = euc-kr
tcpc.rcvEnc = euc-kr
tcpc.rcvCvtEnc = utf-8

# Local development settings - using localhost or development servers
tcpc.0.name = ax_fid
tcpc.0.mainhost = localhost:14811
tcpc.0.backuphost = localhost:14811
tcpc.0.timeout = 90000
tcpc.0.type = axis
tcpc.0.iniconns = 5
tcpc.0.maxconns = 10

tcpc.1.name = ax_fix
tcpc.1.mainhost = localhost:14811
tcpc.1.backuphost = localhost:14811
tcpc.1.timeout = 90000
tcpc.1.type = axis
tcpc.1.iniconns = 5
tcpc.1.maxconns = 10

tcpc.2.name = XWINGK
tcpc.2.mainhost = localhost:14811
tcpc.2.backuphost = localhost:14811
tcpc.2.timeout = 90000
tcpc.2.type = tuxedo
tcpc.2.iniconns = 5
tcpc.2.maxconns = 10

tcpc.xml_axis = AXIS_Config.xml
tcpc.xml_tux = TUX_Config.xml
tcpc.xml_axisfid = FID_Config.xml
